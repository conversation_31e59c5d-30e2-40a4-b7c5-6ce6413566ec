# 钉钉MCP工具标准化开发流程模板

## 1. 模板使用说明

本模板提供了一套完整的、可复用的钉钉MCP工具开发流程。开发者只需要：
1. 提供钉钉API文档
2. 按照模板步骤执行
3. 填入具体的API信息
4. 生成标准化的工具代码

## 2. 开发流程模板

### 阶段1：API分析和设计模板

#### 1.1 API信息提取模板
```
API名称: {API_NAME}
API端点: {API_ENDPOINT}
HTTP方法: {HTTP_METHOD}
API功能: {API_DESCRIPTION}
API类型: [查询类/操作类/管理类]
```

#### 1.2 参数分析模板
```
必需参数:
- {PARAM_NAME}: {PARAM_TYPE} - {PARAM_DESCRIPTION}

可选参数:
- {PARAM_NAME}: {PARAM_TYPE} - {PARAM_DESCRIPTION} (默认值: {DEFAULT_VALUE})

参数验证规则:
- {PARAM_NAME}: {VALIDATION_RULES}
```

#### 1.3 响应结构分析模板
```
成功响应结构:
{
  "errcode": 0,
  "errmsg": "ok",
  "result": {
    // 具体数据结构
  }
}

错误响应:
- 错误码: {ERROR_CODE} - {ERROR_MESSAGE}
```

#### 1.4 MCP工具设计模板
```
工具名称: {TOOL_NAME} (snake_case)
工具描述: {TOOL_DESCRIPTION}
参数映射:
- MCP参数 -> API参数
响应格式: [JSON/TEXT]
```

### 阶段2：代码结构生成模板

#### 2.1 常量定义模板
**文件**: `pkg/dingtalk/constant/api.go`
```go
const (
    {API_CONSTANT_NAME}Key = "{API_ENDPOINT}" // {API_DESCRIPTION}
)
```

**命名规则**:
- 使用PascalCase + Key后缀
- 名称要清晰表达API功能

#### 2.2 响应结构模板
**文件**: `pkg/dingtalk/response/{FEATURE_NAME}.go`
```go
package response

// {RESPONSE_STRUCT_NAME} {API_DESCRIPTION}响应结构
type {RESPONSE_STRUCT_NAME} struct {
    Response
    Result {RESULT_STRUCT_NAME} `json:"result"`
}

// {RESULT_STRUCT_NAME} {API_DESCRIPTION}结果数据
type {RESULT_STRUCT_NAME} struct {
    // 根据API文档定义字段
    {FIELD_NAME} {FIELD_TYPE} `json:"{JSON_TAG}"`
}
```

#### 2.3 客户端方法模板
**文件**: `pkg/dingtalk/{FEATURE_NAME}.go`
```go
package dingtalk

import (
    "net/http"
    
    "github.com/zhaoyunxing92/dingtalk-mcp/pkg/dingtalk/constant"
    "github.com/zhaoyunxing92/dingtalk-mcp/pkg/dingtalk/response"
)

// {CLIENT_METHOD_NAME} {API_DESCRIPTION}
func (ds *DingTalk) {CLIENT_METHOD_NAME}({PARAMETERS}) (*response.{RESPONSE_STRUCT_NAME}, error) {
    var (
        body = map[string]interface{}{
            // 参数映射
            "{API_PARAM_NAME}": {GO_PARAM_NAME},
        }
        data = &response.{RESPONSE_STRUCT_NAME}{}
        err  error
    )
    
    if err = ds.Request(http.Method{HTTP_METHOD}, constant.{API_CONSTANT_NAME}Key, nil, body, data); err != nil {
        return nil, err
    }
    
    return data, nil
}
```

#### 2.4 服务层实现模板
**文件**: `internal/service/{FEATURE_NAME}.go`
```go
package service

import (
    "context"
    "encoding/json"
    "fmt"
    
    "github.com/mark3labs/mcp-go/mcp"
    "github.com/mark3labs/mcp-go/server"
    "github.com/pkg/errors"
    
    "github.com/zhaoyunxing92/dingtalk-mcp/pkg/dingtalk"
)

type {SERVICE_STRUCT_NAME} struct {
    client *dingtalk.DingTalk
}

func New{SERVICE_STRUCT_NAME}(client *dingtalk.DingTalk) *{SERVICE_STRUCT_NAME} {
    return &{SERVICE_STRUCT_NAME}{client: client}
}

// {TOOL_METHOD_NAME} {TOOL_DESCRIPTION}
func (svc *{SERVICE_STRUCT_NAME}) {TOOL_METHOD_NAME}(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
    // 1. 参数提取和验证
    {PARAMETER_EXTRACTION}
    
    // 2. 参数业务逻辑验证
    {PARAMETER_VALIDATION}
    
    // 3. 调用钉钉API
    resp, err := svc.client.{CLIENT_METHOD_NAME}({PARAMETERS})
    if err != nil {
        return nil, fmt.Errorf("调用钉钉API失败: %w", err)
    }
    
    // 4. 格式化响应
    {RESPONSE_FORMATTING}
}

// AddTools 注册MCP工具
func (svc *{SERVICE_STRUCT_NAME}) AddTools(server *server.MCPServer) {
    {TOOL_REGISTRATION}
}
```

### 阶段3：具体代码片段模板

#### 3.1 参数提取模板
```go
// 字符串参数
{PARAM_NAME}, ok := req.Params.Arguments["{MCP_PARAM_NAME}"].(string)
if !ok {
    return nil, errors.New("{MCP_PARAM_NAME}必须是字符串类型")
}

// 整数参数
{PARAM_NAME}, ok := req.Params.Arguments["{MCP_PARAM_NAME}"].(int)
if !ok {
    return nil, errors.New("{MCP_PARAM_NAME}必须是整数类型")
}

// 布尔参数
{PARAM_NAME}, ok := req.Params.Arguments["{MCP_PARAM_NAME}"].(bool)
if !ok {
    return nil, errors.New("{MCP_PARAM_NAME}必须是布尔类型")
}
```

#### 3.2 参数验证模板
```go
// 非空验证
if {PARAM_NAME} == "" {
    return nil, errors.New("{MCP_PARAM_NAME}不能为空")
}

// 数值范围验证
if {PARAM_NAME} < {MIN_VALUE} || {PARAM_NAME} > {MAX_VALUE} {
    return nil, errors.New("{MCP_PARAM_NAME}必须在{MIN_VALUE}到{MAX_VALUE}之间")
}

// 枚举值验证
validValues := []string{"{VALUE1}", "{VALUE2}"}
if !contains(validValues, {PARAM_NAME}) {
    return nil, errors.New("{MCP_PARAM_NAME}必须是以下值之一: " + strings.Join(validValues, ", "))
}
```

#### 3.3 响应格式化模板
```go
// JSON响应
if marshal, err := json.Marshal(resp.Result); err != nil {
    return nil, fmt.Errorf("响应数据序列化失败: %w", err)
} else {
    return mcp.NewToolResultText(string(marshal)), nil
}

// 简单文本响应
return mcp.NewToolResultText({SIMPLE_VALUE}), nil

// 数字转字符串响应
return mcp.NewToolResultText(strconv.Itoa({NUMBER_VALUE})), nil
```

#### 3.4 工具注册模板
```go
{TOOL_VAR_NAME} := mcp.NewTool("{TOOL_NAME}",
    mcp.WithDescription("{TOOL_DESCRIPTION}"),
    {PARAMETER_DEFINITIONS})

server.AddTool({TOOL_VAR_NAME}, svc.{TOOL_METHOD_NAME})
```

#### 3.5 参数定义模板
```go
// 字符串参数
mcp.WithString("{PARAM_NAME}",
    mcp.Required(),
    mcp.Description("{PARAM_DESCRIPTION}"))

// 整数参数
mcp.WithInt("{PARAM_NAME}",
    mcp.Required(),
    mcp.Description("{PARAM_DESCRIPTION}"))

// 布尔参数
mcp.WithBoolean("{PARAM_NAME}",
    mcp.Required(),
    mcp.Description("{PARAM_DESCRIPTION}"))

// 可选参数（去掉mcp.Required()）
mcp.WithString("{PARAM_NAME}",
    mcp.Description("{PARAM_DESCRIPTION}"))
```

## 3. 命名规范模板

### 3.1 命名规则表
| 类型 | 规则 | 示例 |
|------|------|------|
| API常量 | PascalCase + Key | `GetUserListKey` |
| 响应结构 | PascalCase + Response | `GetUserListResponse` |
| 结果结构 | PascalCase | `UserListResult` |
| 服务结构 | PascalCase + Service | `UserService` |
| 客户端方法 | PascalCase | `GetUserList` |
| 服务方法 | PascalCase | `GetUserList` |
| MCP工具名 | snake_case | `get_user_list` |
| 文件名 | snake_case | `user_service.go` |

### 3.2 中文注释模板
```go
// {STRUCT_NAME} {功能描述}，{详细说明}
type {STRUCT_NAME} struct {
    // {字段描述}
    {FieldName} {FieldType} `json:"{json_tag}"`
}

// {METHOD_NAME} {方法功能描述}
// 参数说明：
// - {param1}: {参数1说明}
// - {param2}: {参数2说明}
// 返回值：{返回值说明}
func {METHOD_NAME}({parameters}) ({return_type}, error) {
    // 方法实现
}
```

## 4. 质量检查模板

### 4.1 代码检查清单
- [ ] API常量定义正确
- [ ] 响应结构完整
- [ ] 参数提取安全
- [ ] 参数验证完整
- [ ] 错误处理合理
- [ ] 响应格式化正确
- [ ] 工具注册完成
- [ ] 注释文档完整
- [ ] 命名规范一致

### 4.2 功能测试模板
```go
func Test{SERVICE_NAME}_{TOOL_METHOD_NAME}(t *testing.T) {
    tests := []struct {
        name    string
        args    map[string]interface{}
        want    string
        wantErr bool
    }{
        {
            name: "正常情况",
            args: map[string]interface{}{
                "{param1}": "{value1}",
                "{param2}": {value2},
            },
            want:    "{expected_result}",
            wantErr: false,
        },
        {
            name: "参数错误",
            args: map[string]interface{}{
                "{param1}": nil,
            },
            wantErr: true,
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            // 测试实现
        })
    }
}
```
