# 钉钉MCP工具标准开发流程梳理

## 1. 开发流程概览

基于现有工具分析，钉钉MCP工具的开发遵循以下标准流程：

```
API文档分析 → 常量定义 → 响应结构 → 客户端方法 → 服务层实现 → 工具注册 → 主程序集成 → 测试验证
```

## 2. 详细开发步骤

### 阶段1：API文档分析和设计
**目标**: 理解API功能、参数和响应结构

#### 1.1 API功能分析
- 确定API的业务功能和使用场景
- 识别API类型（查询类/操作类/管理类）
- 分析API的输入输出关系

#### 1.2 参数结构分析
- 必需参数 vs 可选参数
- 参数类型和验证规则
- 默认值和限制条件
- 参数之间的依赖关系

#### 1.3 响应结构分析
- 成功响应的数据结构
- 错误码和错误信息
- 分页机制（如适用）
- 嵌套数据结构

#### 1.4 MCP工具设计
- 确定工具名称（snake_case）
- 设计参数映射关系
- 确定响应数据格式
- 编写工具描述文档

### 阶段2：基础代码结构实现

#### 2.1 常量定义
**文件**: `pkg/dingtalk/constant/api.go`
```go
const (
    YourApiEndpointKey = "/path/to/api" // API功能描述
)
```

**命名规范**:
- 使用PascalCase + Key后缀
- 名称要能清晰表达API功能
- 按功能模块分组组织

#### 2.2 响应结构定义
**文件**: `pkg/dingtalk/response/your_feature.go`
```go
// YourApiResponse API响应结构
type YourApiResponse struct {
    Response
    Result YourResultStruct `json:"result"`
}

// YourResultStruct 具体结果数据结构
type YourResultStruct struct {
    Field1 string `json:"field1"`
    Field2 int    `json:"field2"`
    // 根据API文档定义字段
}
```

**设计原则**:
- 继承基础Response结构
- 使用Result字段包装实际数据
- JSON标签与API文档保持一致
- 添加中文注释说明

#### 2.3 数据模型定义（如需要）
**文件**: `pkg/dingtalk/models/your_feature/`
```go
// 定义请求和响应中使用的数据模型
type YourDataModel struct {
    Field1 string `json:"field1"`
    Field2 int    `json:"field2"`
}
```

### 阶段3：客户端方法实现

#### 3.1 客户端方法
**文件**: `pkg/dingtalk/your_feature.go`
```go
// YourApiMethod API功能的中文描述
func (ds *DingTalk) YourApiMethod(param1 string, param2 int) (*response.YourApiResponse, error) {
    var (
        body = map[string]interface{}{
            "param1": param1,
            "param2": param2,
        }
        data = &response.YourApiResponse{}
        err  error
    )
    
    if err = ds.Request(http.MethodPost, constant.YourApiEndpointKey, nil, body, data); err != nil {
        return nil, err
    }
    
    return data, nil
}
```

**实现要点**:
- 方法名使用PascalCase
- 参数类型要与MCP工具参数匹配
- 使用统一的Request方法
- 返回完整的响应结构

### 阶段4：服务层实现

#### 4.1 服务结构定义
**文件**: `internal/service/your_feature.go`
```go
type YourService struct {
    client *dingtalk.DingTalk
}

func NewYourService(client *dingtalk.DingTalk) *YourService {
    return &YourService{client: client}
}
```

#### 4.2 MCP工具处理方法
```go
// YourToolMethod 工具功能的中文描述
func (svc *YourService) YourToolMethod(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
    // 1. 安全的参数提取和验证
    param1, ok := req.Params.Arguments["param1"].(string)
    if !ok {
        return nil, errors.New("param1必须是字符串类型")
    }
    
    param2, ok := req.Params.Arguments["param2"].(int)
    if !ok {
        return nil, errors.New("param2必须是整数类型")
    }
    
    // 2. 参数业务逻辑验证
    if param1 == "" {
        return nil, errors.New("param1不能为空")
    }
    
    // 3. 调用钉钉API
    resp, err := svc.client.YourApiMethod(param1, param2)
    if err != nil {
        return nil, fmt.Errorf("调用钉钉API失败: %w", err)
    }
    
    // 4. 格式化响应
    if marshal, err := json.Marshal(resp.Result); err != nil {
        return nil, fmt.Errorf("响应数据序列化失败: %w", err)
    } else {
        return mcp.NewToolResultText(string(marshal)), nil
    }
}
```

#### 4.3 工具注册方法
```go
// AddTools 注册MCP工具
func (svc *YourService) AddTools(server *server.MCPServer) {
    yourTool := mcp.NewTool("your_tool_name",
        mcp.WithDescription("工具功能的详细描述"),
        mcp.WithString("param1",
            mcp.Required(),
            mcp.Description("参数1的详细描述和使用说明")),
        mcp.WithInt("param2",
            mcp.Required(),
            mcp.Description("参数2的详细描述和使用说明")))
    
    server.AddTool(yourTool, svc.YourToolMethod)
}
```

### 阶段5：主程序集成

#### 5.1 服务注册
**文件**: `main.go`
```go
func main() {
    // ... 现有代码 ...
    
    // 注册新服务
    service.NewYourService(client).AddTools(svc)
    
    // ... 现有代码 ...
}
```

#### 5.2 配置文件更新
**文件**: `claude_desktop_config.json`
```json
{
  "mcpServers": {
    "dingtalk-mcp": {
      "autoApprove": [
        "existing_tools",
        "your_new_tool_name"
      ]
    }
  }
}
```

### 阶段6：测试和验证

#### 6.1 单元测试
```go
func TestYourService_YourToolMethod(t *testing.T) {
    // 测试用例实现
}
```

#### 6.2 集成测试
- 使用真实的钉钉API进行测试
- 验证参数传递和响应处理
- 测试错误场景处理

#### 6.3 MCP客户端测试
- 在Claude Desktop中测试工具调用
- 验证参数提示和响应显示
- 测试错误信息展示

## 3. 关键步骤和最佳实践

### 3.1 参数处理最佳实践
1. **类型安全**: 使用ok模式进行类型断言
2. **参数验证**: 检查必需参数和业务逻辑
3. **错误信息**: 提供清晰的中文错误提示
4. **默认值**: 为可选参数提供合理默认值

### 3.2 错误处理最佳实践
1. **分层处理**: 在不同层次进行相应的错误处理
2. **错误包装**: 使用fmt.Errorf包装错误信息
3. **用户友好**: 提供对用户有意义的错误信息
4. **错误传播**: 保持错误链的完整性

### 3.3 响应处理最佳实践
1. **数据提取**: 只返回用户需要的数据
2. **格式统一**: 使用JSON格式返回复杂数据
3. **简单数据**: 直接返回字符串格式的简单数据
4. **错误检查**: 处理JSON序列化可能的错误

### 3.4 代码质量最佳实践
1. **命名规范**: 遵循项目的命名约定
2. **注释完整**: 提供中文注释和文档
3. **结构清晰**: 保持代码结构的一致性
4. **依赖管理**: 合理组织包依赖关系

## 4. 开发检查清单

### 4.1 代码实现检查
- [ ] API常量定义正确
- [ ] 响应结构完整
- [ ] 客户端方法实现
- [ ] 服务层方法实现
- [ ] 工具注册完成
- [ ] 主程序集成

### 4.2 质量检查
- [ ] 参数验证完整
- [ ] 错误处理合理
- [ ] 注释文档完整
- [ ] 命名规范一致
- [ ] 代码结构清晰

### 4.3 功能检查
- [ ] API调用正确
- [ ] 响应处理正确
- [ ] MCP工具可用
- [ ] 错误场景处理
- [ ] 边界条件测试

### 4.4 集成检查
- [ ] 编译通过
- [ ] 单元测试通过
- [ ] 集成测试通过
- [ ] MCP客户端测试通过
- [ ] 文档更新完成

## 5. 常见问题和解决方案

### 5.1 参数类型不匹配
**问题**: MCP传递的参数类型与期望不符
**解决**: 使用类型断言的ok模式进行安全检查

### 5.2 API调用失败
**问题**: 钉钉API返回错误
**解决**: 检查参数格式、权限配置和API文档

### 5.3 响应数据格式问题
**问题**: JSON序列化失败或数据结构不匹配
**解决**: 检查响应结构定义和JSON标签

### 5.4 MCP工具不可见
**问题**: 工具未在Claude Desktop中显示
**解决**: 检查工具注册和配置文件更新

## 6. 开发效率优化建议

### 6.1 模板化开发
- 创建代码模板文件
- 使用代码生成工具
- 建立标准化的开发流程

### 6.2 自动化测试
- 集成CI/CD流程
- 自动化API测试
- 回归测试套件

### 6.3 文档维护
- API文档同步更新
- 工具使用说明
- 开发者指南维护

### 6.4 代码复用
- 提取公共组件
- 建立工具库
- 共享最佳实践
