# 错误处理和参数验证标准模板

## 1. 参数验证模板

### 1.1 类型安全的参数提取模板

#### 1.1.1 字符串参数提取
```go
// 必需字符串参数
{paramName}, ok := req.Params.Arguments["{mcpParamName}"].(string)
if !ok {
    return nil, errors.New("{mcpParamName}必须是字符串类型")
}

// 可选字符串参数
{paramName} := ""
if val, ok := req.Params.Arguments["{mcpParamName}"].(string); ok {
    {paramName} = val
} else if req.Params.Arguments["{mcpParamName}"] != nil {
    return nil, errors.New("{mcpParamName}必须是字符串类型")
}
```

#### 1.1.2 整数参数提取
```go
// 必需整数参数
{paramName}, ok := req.Params.Arguments["{mcpParamName}"].(int)
if !ok {
    // 尝试从float64转换（JSON数字默认为float64）
    if floatVal, ok := req.Params.Arguments["{mcpParamName}"].(float64); ok {
        {paramName} = int(floatVal)
    } else {
        return nil, errors.New("{mcpParamName}必须是整数类型")
    }
}

// 可选整数参数（带默认值）
{paramName} := {defaultValue}
if val, ok := req.Params.Arguments["{mcpParamName}"].(int); ok {
    {paramName} = val
} else if floatVal, ok := req.Params.Arguments["{mcpParamName}"].(float64); ok {
    {paramName} = int(floatVal)
} else if req.Params.Arguments["{mcpParamName}"] != nil {
    return nil, errors.New("{mcpParamName}必须是整数类型")
}
```

#### 1.1.3 布尔参数提取
```go
// 必需布尔参数
{paramName}, ok := req.Params.Arguments["{mcpParamName}"].(bool)
if !ok {
    return nil, errors.New("{mcpParamName}必须是布尔类型")
}

// 可选布尔参数（带默认值）
{paramName} := {defaultValue}
if val, ok := req.Params.Arguments["{mcpParamName}"].(bool); ok {
    {paramName} = val
} else if req.Params.Arguments["{mcpParamName}"] != nil {
    return nil, errors.New("{mcpParamName}必须是布尔类型")
}
```

#### 1.1.4 数组参数提取
```go
// 字符串数组参数
{paramName}Interface, ok := req.Params.Arguments["{mcpParamName}"].([]interface{})
if !ok {
    return nil, errors.New("{mcpParamName}必须是数组类型")
}

{paramName} := make([]string, len({paramName}Interface))
for i, v := range {paramName}Interface {
    if str, ok := v.(string); ok {
        {paramName}[i] = str
    } else {
        return nil, errors.New("{mcpParamName}数组元素必须是字符串类型")
    }
}
```

### 1.2 业务逻辑验证模板

#### 1.2.1 非空验证
```go
// 字符串非空验证
if strings.TrimSpace({paramName}) == "" {
    return nil, errors.New("{mcpParamName}不能为空")
}

// 数组非空验证
if len({paramName}) == 0 {
    return nil, errors.New("{mcpParamName}不能为空数组")
}
```

#### 1.2.2 长度验证
```go
// 字符串长度验证
if len({paramName}) > {maxLength} {
    return nil, errors.New("{mcpParamName}长度不能超过{maxLength}个字符")
}

if len({paramName}) < {minLength} {
    return nil, errors.New("{mcpParamName}长度不能少于{minLength}个字符")
}

// 数组长度验证
if len({paramName}) > {maxCount} {
    return nil, errors.New("{mcpParamName}数组长度不能超过{maxCount}个元素")
}
```

#### 1.2.3 数值范围验证
```go
// 整数范围验证
if {paramName} < {minValue} {
    return nil, errors.New("{mcpParamName}不能小于{minValue}")
}

if {paramName} > {maxValue} {
    return nil, errors.New("{mcpParamName}不能大于{maxValue}")
}

// 组合范围验证
if {paramName} < {minValue} || {paramName} > {maxValue} {
    return nil, fmt.Errorf("{mcpParamName}必须在%d到%d之间", {minValue}, {maxValue})
}
```

#### 1.2.4 枚举值验证
```go
// 字符串枚举验证
validValues := []string{"{value1}", "{value2}", "{value3}"}
valid := false
for _, v := range validValues {
    if {paramName} == v {
        valid = true
        break
    }
}
if !valid {
    return nil, fmt.Errorf("{mcpParamName}必须是以下值之一: %s", strings.Join(validValues, ", "))
}

// 使用辅助函数的枚举验证
func contains(slice []string, item string) bool {
    for _, s := range slice {
        if s == item {
            return true
        }
    }
    return false
}

if !contains(validValues, {paramName}) {
    return nil, fmt.Errorf("{mcpParamName}必须是以下值之一: %s", strings.Join(validValues, ", "))
}
```

#### 1.2.5 格式验证
```go
// 邮箱格式验证
emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
if !emailRegex.MatchString({paramName}) {
    return nil, errors.New("{mcpParamName}格式不正确，请输入有效的邮箱地址")
}

// 手机号格式验证
phoneRegex := regexp.MustCompile(`^1[3-9]\d{9}$`)
if !phoneRegex.MatchString({paramName}) {
    return nil, errors.New("{mcpParamName}格式不正确，请输入有效的手机号码")
}

// 用户ID格式验证（钉钉用户ID通常是数字字符串）
if !regexp.MustCompile(`^\d+$`).MatchString({paramName}) {
    return nil, errors.New("{mcpParamName}格式不正确，用户ID应该是数字字符串")
}
```

#### 1.2.6 依赖参数验证
```go
// 参数互斥验证
if {param1} != "" && {param2} != "" {
    return nil, errors.New("{param1}和{param2}不能同时指定")
}

// 参数依赖验证
if {param1} != "" && {param2} == "" {
    return nil, errors.New("指定{param1}时必须同时指定{param2}")
}

// 条件参数验证
if {conditionParam} == "{conditionValue}" && {requiredParam} == "" {
    return nil, errors.New("当{conditionParam}为{conditionValue}时，{requiredParam}为必需参数")
}
```

## 2. 错误处理模板

### 2.1 分层错误处理

#### 2.1.1 参数层错误
```go
// 参数类型错误
return nil, errors.New("{paramName}参数类型错误：期望{expectedType}，实际{actualType}")

// 参数值错误
return nil, fmt.Errorf("{paramName}参数值错误：%s", {errorDetails})

// 参数缺失错误
return nil, errors.New("缺少必需参数：{paramName}")
```

#### 2.1.2 业务逻辑层错误
```go
// 业务规则违反
return nil, fmt.Errorf("业务规则验证失败：%s", {ruleDescription})

// 权限不足错误
return nil, errors.New("权限不足：无法执行此操作")

// 资源不存在错误
return nil, fmt.Errorf("资源不存在：%s", {resourceId})
```

#### 2.1.3 API调用层错误
```go
// API调用失败
resp, err := svc.client.{ApiMethod}({parameters})
if err != nil {
    return nil, fmt.Errorf("调用钉钉API失败: %w", err)
}

// API响应错误
if !resp.Ok() {
    return nil, fmt.Errorf("钉钉API返回错误 (错误码: %d): %s", resp.Code, resp.Msg)
}
```

#### 2.1.4 数据处理层错误
```go
// JSON序列化错误
if marshal, err := json.Marshal(resp.Result); err != nil {
    return nil, fmt.Errorf("响应数据序列化失败: %w", err)
} else {
    return mcp.NewToolResultText(string(marshal)), nil
}

// 数据转换错误
if convertedValue, err := convertData(rawValue); err != nil {
    return nil, fmt.Errorf("数据转换失败: %w", err)
}
```

### 2.2 错误信息国际化模板

#### 2.2.1 错误消息常量
```go
const (
    // 参数错误消息
    ErrParamRequired     = "%s为必需参数"
    ErrParamType         = "%s必须是%s类型"
    ErrParamEmpty        = "%s不能为空"
    ErrParamTooLong      = "%s长度不能超过%d个字符"
    ErrParamTooShort     = "%s长度不能少于%d个字符"
    ErrParamOutOfRange   = "%s必须在%d到%d之间"
    ErrParamInvalidValue = "%s必须是以下值之一: %s"
    ErrParamInvalidFormat = "%s格式不正确"
    
    // 业务错误消息
    ErrResourceNotFound  = "资源不存在: %s"
    ErrPermissionDenied  = "权限不足: %s"
    ErrBusinessRule      = "业务规则验证失败: %s"
    
    // API错误消息
    ErrApiCall           = "调用钉钉API失败: %s"
    ErrApiResponse       = "钉钉API返回错误 (错误码: %d): %s"
    ErrDataSerialization = "响应数据序列化失败: %s"
)
```

#### 2.2.2 错误消息使用
```go
// 使用错误消息常量
if {paramName} == "" {
    return nil, fmt.Errorf(ErrParamEmpty, "{paramDisplayName}")
}

if {paramName} < {minValue} || {paramName} > {maxValue} {
    return nil, fmt.Errorf(ErrParamOutOfRange, "{paramDisplayName}", {minValue}, {maxValue})
}
```

### 2.3 错误恢复和重试模板

#### 2.3.1 API调用重试
```go
const maxRetries = 3

func (svc *{ServiceName}) callApiWithRetry({parameters}) (*response.{ResponseType}, error) {
    var lastErr error
    
    for i := 0; i < maxRetries; i++ {
        resp, err := svc.client.{ApiMethod}({parameters})
        if err == nil {
            return resp, nil
        }
        
        lastErr = err
        
        // 判断是否应该重试
        if !shouldRetry(err) {
            break
        }
        
        // 指数退避
        time.Sleep(time.Duration(1<<i) * time.Second)
    }
    
    return nil, fmt.Errorf("API调用失败，已重试%d次: %w", maxRetries, lastErr)
}

func shouldRetry(err error) bool {
    // 网络错误或临时错误应该重试
    if strings.Contains(err.Error(), "timeout") ||
       strings.Contains(err.Error(), "connection") {
        return true
    }
    return false
}
```

#### 2.3.2 优雅降级处理
```go
// 主要功能失败时的降级处理
resp, err := svc.client.{PrimaryApiMethod}({parameters})
if err != nil {
    // 尝试降级方案
    if fallbackResp, fallbackErr := svc.client.{FallbackApiMethod}({fallbackParameters}); fallbackErr == nil {
        return formatFallbackResponse(fallbackResp), nil
    }
    
    // 降级方案也失败，返回原始错误
    return nil, fmt.Errorf("主要功能和降级方案都失败: %w", err)
}
```

## 3. 完整的参数验证和错误处理示例

### 3.1 综合示例
```go
func (svc *UserService) GetUserInfo(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
    // 1. 参数提取和类型验证
    userID, ok := req.Params.Arguments["user_id"].(string)
    if !ok {
        return nil, errors.New("user_id必须是字符串类型")
    }
    
    includeDetail := false
    if val, ok := req.Params.Arguments["include_detail"].(bool); ok {
        includeDetail = val
    } else if req.Params.Arguments["include_detail"] != nil {
        return nil, errors.New("include_detail必须是布尔类型")
    }
    
    // 2. 参数业务逻辑验证
    if strings.TrimSpace(userID) == "" {
        return nil, errors.New("user_id不能为空")
    }
    
    if !regexp.MustCompile(`^\d+$`).MatchString(userID) {
        return nil, errors.New("user_id格式不正确，应该是数字字符串")
    }
    
    // 3. API调用和错误处理
    resp, err := svc.client.GetUserInfo(userID, includeDetail)
    if err != nil {
        // 特定错误处理
        if strings.Contains(err.Error(), "user not found") {
            return nil, fmt.Errorf("用户不存在: %s", userID)
        }
        return nil, fmt.Errorf("获取用户信息失败: %w", err)
    }
    
    // 4. 响应数据处理和错误处理
    if marshal, err := json.Marshal(resp.Result); err != nil {
        return nil, fmt.Errorf("响应数据序列化失败: %w", err)
    } else {
        return mcp.NewToolResultText(string(marshal)), nil
    }
}
```

### 3.2 辅助函数模板
```go
// 参数验证辅助函数
func validateStringParam(args map[string]interface{}, paramName string, required bool, maxLength int) (string, error) {
    val, ok := args[paramName].(string)
    if !ok {
        if required {
            return "", fmt.Errorf("%s必须是字符串类型", paramName)
        }
        return "", nil
    }
    
    if required && strings.TrimSpace(val) == "" {
        return "", fmt.Errorf("%s不能为空", paramName)
    }
    
    if maxLength > 0 && len(val) > maxLength {
        return "", fmt.Errorf("%s长度不能超过%d个字符", paramName, maxLength)
    }
    
    return val, nil
}

// 错误分类辅助函数
func classifyError(err error) string {
    errStr := err.Error()
    switch {
    case strings.Contains(errStr, "not found"):
        return "RESOURCE_NOT_FOUND"
    case strings.Contains(errStr, "permission"):
        return "PERMISSION_DENIED"
    case strings.Contains(errStr, "timeout"):
        return "TIMEOUT"
    case strings.Contains(errStr, "network"):
        return "NETWORK_ERROR"
    default:
        return "UNKNOWN_ERROR"
    }
}
```
