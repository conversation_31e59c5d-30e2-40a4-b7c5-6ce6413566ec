# MCP工具注册机制标准模板

## 1. MCP工具注册流程概述

MCP工具注册包含以下关键步骤：
1. 工具定义（Tool Definition）
2. 参数定义（Parameter Definition）
3. 处理函数绑定（Handler Binding）
4. 服务注册（Service Registration）
5. 主程序集成（Main Integration）

## 2. 工具定义模板

### 2.1 基础工具定义结构
```go
{toolVariableName} := mcp.NewTool("{tool_name}",
    mcp.WithDescription("{tool_description}"),
    {parameter_definitions})
```

### 2.2 工具命名规范
```go
// 工具名称：使用snake_case，动词开头
"get_user_list"           // ✅ 获取用户列表
"send_message"            // ✅ 发送消息
"create_department"       // ✅ 创建部门
"update_user_info"        // ✅ 更新用户信息
"delete_conversation"     // ✅ 删除会话

// 避免的命名方式
"getUserList"             // ❌ 不要使用camelCase
"get-user-list"           // ❌ 不要使用连字符
"GetUserList"             // ❌ 不要使用PascalCase
```

### 2.3 工具描述模板
```go
// 简洁明确的功能描述
mcp.WithDescription("获取企业员工列表")
mcp.WithDescription("发送工作通知消息")
mcp.WithDescription("创建企业部门")

// 详细描述模板（适用于复杂工具）
mcp.WithDescription(`获取企业员工列表
支持按部门筛选和分页查询
返回员工的基础信息包括姓名、用户ID、部门等`)
```

## 3. 参数定义模板

### 3.1 字符串参数定义
```go
// 必需字符串参数
mcp.WithString("{param_name}",
    mcp.Required(),
    mcp.Description("{parameter_description}"))

// 可选字符串参数
mcp.WithString("{param_name}",
    mcp.Description("{parameter_description}"))

// 带详细说明的字符串参数
mcp.WithString("user_ids",
    mcp.Required(),
    mcp.Description(`接收者的用户ID列表，用英文逗号分隔
示例: "user001,user002,user003"
最大用户列表长度: 100`))
```

### 3.2 整数参数定义
```go
// 必需整数参数
mcp.WithInt("{param_name}",
    mcp.Required(),
    mcp.Description("{parameter_description}"))

// 可选整数参数
mcp.WithInt("{param_name}",
    mcp.Description("{parameter_description}"))

// 带范围说明的整数参数
mcp.WithInt("page_size",
    mcp.Description(`每页返回的记录数
取值范围: 1-100
默认值: 20`))
```

### 3.3 布尔参数定义
```go
// 必需布尔参数
mcp.WithBoolean("{param_name}",
    mcp.Required(),
    mcp.Description("{parameter_description}"))

// 可选布尔参数
mcp.WithBoolean("{param_name}",
    mcp.Description("{parameter_description}"))

// 带选项说明的布尔参数
mcp.WithBoolean("only_active",
    mcp.Required(),
    mcp.Description(`是否只包含激活用户：
* true：只包含激活钉钉的用户
* false：包含所有用户（包括未激活）`))
```

### 3.4 复杂参数定义示例
```go
// 多个参数的工具定义
sendMessageTool := mcp.NewTool("send_corp_message",
    mcp.WithDescription("发送企业工作通知消息"),
    mcp.WithString("user_ids",
        mcp.Required(),
        mcp.Description(`接收者的用户ID列表，用英文逗号分隔
最大用户列表长度: 100`)),
    mcp.WithString("message_type",
        mcp.Required(),
        mcp.Description(`消息类型：
* text: 纯文本消息
* markdown: Markdown格式消息`)),
    mcp.WithString("content",
        mcp.Required(),
        mcp.Description("消息内容，最长不超过2048个字符")),
    mcp.WithString("title",
        mcp.Description(`消息标题（仅markdown类型需要）
最长不超过128个字符`)),
    mcp.WithBoolean("at_all",
        mcp.Description(`是否@所有人
默认值: false`)))
```

## 4. 处理函数模板

### 4.1 标准处理函数签名
```go
func (svc *{ServiceName}) {ToolMethodName}(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
    // 处理函数实现
}
```

### 4.2 处理函数实现模板
```go
// {ToolMethodName} {tool_description}
func (svc *{ServiceName}) {ToolMethodName}(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
    // 1. 参数提取和验证
    {parameter_extraction_code}
    
    // 2. 业务逻辑验证
    {business_validation_code}
    
    // 3. 调用钉钉API
    resp, err := svc.client.{ClientMethodName}({parameters})
    if err != nil {
        return nil, fmt.Errorf("调用钉钉API失败: %w", err)
    }
    
    // 4. 响应格式化
    {response_formatting_code}
}
```

### 4.3 处理函数命名规范
```go
// 处理函数名称：使用PascalCase，与工具功能对应
func (svc *UserService) GetUserList(...)      // 对应 get_user_list
func (svc *MessageService) SendMessage(...)   // 对应 send_message
func (svc *DeptService) CreateDepartment(...) // 对应 create_department
```

## 5. 服务注册模板

### 5.1 AddTools方法模板
```go
// AddTools 注册MCP工具到服务器
func (svc *{ServiceName}) AddTools(server *server.MCPServer) {
    // 定义工具1
    {tool1VariableName} := mcp.NewTool("{tool1_name}",
        mcp.WithDescription("{tool1_description}"),
        {tool1_parameters})
    
    // 定义工具2
    {tool2VariableName} := mcp.NewTool("{tool2_name}",
        mcp.WithDescription("{tool2_description}"),
        {tool2_parameters})
    
    // 注册工具和处理函数
    server.AddTool({tool1VariableName}, svc.{Tool1MethodName})
    server.AddTool({tool2VariableName}, svc.{Tool2MethodName})
}
```

### 5.2 完整服务注册示例
```go
// AddTools 注册用户相关的MCP工具
func (svc *UserService) AddTools(server *server.MCPServer) {
    // 获取用户列表工具
    getUserListTool := mcp.NewTool("get_user_list",
        mcp.WithDescription("获取企业用户列表"),
        mcp.WithInt("dept_id",
            mcp.Description("部门ID，不指定则获取所有部门用户")),
        mcp.WithInt("page_size",
            mcp.Description("每页返回数量，默认20，最大100")),
        mcp.WithInt("cursor",
            mcp.Description("分页游标，默认0")))
    
    // 获取用户详情工具
    getUserInfoTool := mcp.NewTool("get_user_info",
        mcp.WithDescription("获取用户详细信息"),
        mcp.WithString("user_id",
            mcp.Required(),
            mcp.Description("用户ID")),
        mcp.WithBoolean("include_roles",
            mcp.Description("是否包含角色信息，默认false")))
    
    // 注册工具
    server.AddTool(getUserListTool, svc.GetUserList)
    server.AddTool(getUserInfoTool, svc.GetUserInfo)
}
```

## 6. 主程序集成模板

### 6.1 main.go集成模板
```go
func main() {
    // ... 现有初始化代码 ...
    
    // 创建钉钉客户端
    client := dingtalk.NewClient(id, key, secret)
    
    // 创建MCP服务器
    svc := server.NewMCPServer(
        "dingtalk",
        "1.0.0",
        server.WithLogging(),
        server.WithResourceCapabilities(true, true),
    )
    
    // 注册现有服务
    service.NewEmployeeService(client).AddTools(svc)
    service.NewMessageService(client).AddTools(svc)
    
    // 注册新服务
    service.New{ServiceName}(client).AddTools(svc)
    
    // 启动服务器
    if err := server.ServeStdio(svc); err != nil {
        fmt.Printf("Server error: %v\n", err)
    }
}
```

### 6.2 配置文件更新模板
```json
{
  "mcpServers": {
    "dingtalk-mcp": {
      "command": "./dingtalk-mcp",
      "args": [],
      "env": {
        "DINGTALK_AGENT_ID": "your_agent_id",
        "DINGTALK_KEY": "your_app_key",
        "DINGTALK_SECRET": "your_app_secret"
      },
      "disabled": false,
      "autoApprove": [
        "existing_tool_1",
        "existing_tool_2",
        "{new_tool_name}"
      ],
      "timeout": 60
    }
  }
}
```

## 7. 工具分组和组织模板

### 7.1 按功能模块分组
```go
// 用户管理相关工具
func (svc *UserService) AddTools(server *server.MCPServer) {
    // 用户查询工具
    server.AddTool(getUserListTool, svc.GetUserList)
    server.AddTool(getUserInfoTool, svc.GetUserInfo)
    
    // 用户管理工具
    server.AddTool(createUserTool, svc.CreateUser)
    server.AddTool(updateUserTool, svc.UpdateUser)
    server.AddTool(deleteUserTool, svc.DeleteUser)
}

// 消息通信相关工具
func (svc *MessageService) AddTools(server *server.MCPServer) {
    // 消息发送工具
    server.AddTool(sendTextMessageTool, svc.SendTextMessage)
    server.AddTool(sendMarkdownMessageTool, svc.SendMarkdownMessage)
    
    // 消息管理工具
    server.AddTool(recallMessageTool, svc.RecallMessage)
    server.AddTool(getMessageStatusTool, svc.GetMessageStatus)
}
```

### 7.2 工具依赖关系处理
```go
// 有依赖关系的工具注册
func (svc *WorkflowService) AddTools(server *server.MCPServer) {
    // 基础工具（被其他工具依赖）
    server.AddTool(createWorkflowTool, svc.CreateWorkflow)
    
    // 依赖基础工具的高级工具
    server.AddTool(startWorkflowTool, svc.StartWorkflow)
    server.AddTool(approveWorkflowTool, svc.ApproveWorkflow)
    
    // 查询工具（独立）
    server.AddTool(getWorkflowStatusTool, svc.GetWorkflowStatus)
}
```

## 8. 工具测试模板

### 8.1 工具注册测试
```go
func TestUserService_AddTools(t *testing.T) {
    client := dingtalk.NewClient(1, "test_key", "test_secret")
    service := NewUserService(client)
    
    server := server.NewMCPServer("test", "1.0.0")
    service.AddTools(server)
    
    // 验证工具是否正确注册
    tools := server.ListTools()
    expectedTools := []string{"get_user_list", "get_user_info"}
    
    for _, expected := range expectedTools {
        found := false
        for _, tool := range tools {
            if tool.Name == expected {
                found = true
                break
            }
        }
        assert.True(t, found, "Tool %s should be registered", expected)
    }
}
```

### 8.2 工具调用测试
```go
func TestUserService_GetUserList(t *testing.T) {
    client := dingtalk.NewClient(1, "test_key", "test_secret")
    service := NewUserService(client)
    
    req := mcp.CallToolRequest{
        Params: mcp.CallToolRequestParams{
            Arguments: map[string]interface{}{
                "dept_id":   1,
                "page_size": 20,
                "cursor":    0,
            },
        },
    }
    
    result, err := service.GetUserList(context.Background(), req)
    assert.NoError(t, err)
    assert.NotNil(t, result)
}
```

## 9. 最佳实践总结

### 9.1 工具设计原则
1. **单一职责**：每个工具只负责一个明确的功能
2. **参数简洁**：避免过多的可选参数
3. **描述清晰**：提供详细的工具和参数描述
4. **错误友好**：提供清晰的错误信息
5. **一致性**：保持命名和结构的一致性

### 9.2 注册顺序建议
1. 先注册基础查询工具
2. 再注册操作类工具
3. 最后注册管理类工具
4. 相关工具放在一起注册

### 9.3 维护性考虑
1. 使用常量定义工具名称
2. 集中管理工具描述
3. 统一参数验证逻辑
4. 提供完整的测试覆盖
