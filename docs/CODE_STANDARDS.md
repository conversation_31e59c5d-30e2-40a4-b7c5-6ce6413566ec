# 钉钉MCP项目代码结构和命名规范

## 目录结构规范

### 标准目录组织

```
dingtalk-mcp/
├── main.go                          # 程序入口点
├── internal/                        # 内部包，不对外暴露
│   └── service/                     # MCP服务层
│       ├── employee.go              # 员工相关服务
│       ├── message.go               # 消息相关服务
│       └── {feature}_service.go     # 其他功能服务
├── pkg/                             # 可重用的包
│   └── dingtalk/                    # 钉钉SDK封装
│       ├── dingtakl.go              # 主客户端
│       ├── employee.go              # 员工API封装
│       ├── message.go               # 消息API封装
│       ├── {feature}.go             # 其他功能API封装
│       ├── cache/                   # 缓存实现
│       │   ├── cache.go             # 缓存接口
│       │   ├── memory/              # 内存缓存实现
│       │   └── file/                # 文件缓存实现
│       ├── constant/                # 常量定义
│       │   ├── api.go               # API端点常量
│       │   └── genre.go             # 其他常量
│       ├── models/                  # 数据模型
│       │   └── message/             # 消息模型
│       ├── request/                 # 请求模型
│       └── response/                # 响应模型
└── docs/                            # 文档目录
    ├── DEVELOPMENT_GUIDE.md         # 开发指南
    ├── TOOL_GENERATOR_PROMPT.md     # 工具生成模板
    └── CODE_STANDARDS.md            # 本文档
```

### 文件命名规则

1. **Go源文件**：使用 `snake_case` 命名
   - ✅ `employee_service.go`
   - ✅ `message_handler.go`
   - ❌ `EmployeeService.go`
   - ❌ `messageHandler.go`

2. **测试文件**：在源文件名后添加 `_test.go`
   - ✅ `employee_service_test.go`
   - ✅ `message_handler_test.go`

3. **文档文件**：使用 `UPPERCASE` 和下划线
   - ✅ `DEVELOPMENT_GUIDE.md`
   - ✅ `CODE_STANDARDS.md`
   - ❌ `development-guide.md`

## 命名规范

### 1. 包名 (Package Names)

- 使用小写字母
- 简短且有意义
- 不使用下划线或混合大小写

```go
package service     // ✅
package dingtalk    // ✅
package msgHandler  // ❌ 
package msg_handler // ❌
```

### 2. 类型名 (Type Names)

- 使用 `PascalCase`
- 首字母大写表示导出，小写表示私有

```go
type EmployeeService struct {}  // ✅ 导出类型
type messageHandler struct {}   // ✅ 私有类型
type employeeService struct {}  // ❌ 不一致
type Employee_Service struct {} // ❌ 不要使用下划线
```

### 3. 方法名和函数名 (Method/Function Names)

- 使用 `PascalCase`
- 首字母大写表示导出，小写表示私有
- 动词开头，表达清晰的操作意图

```go
func GetEmployeeList() {}           // ✅ 导出函数
func sendMessage() {}               // ✅ 私有函数
func NewEmployeeService() {}        // ✅ 构造函数
func (s *Service) AddTools() {}     // ✅ 方法

func get_employee_list() {}         // ❌ 不要使用下划线
func GetemployeeList() {}           // ❌ 不正确的大小写
```

### 4. 变量名 (Variable Names)

- 使用 `camelCase`
- 缩写词保持大写（如 API, HTTP, URL）

```go
var employeeList []Employee    // ✅
var httpClient *http.Client    // ✅
var apiKey string             // ✅
var userID string             // ✅

var employee_list []Employee  // ❌ 不要使用下划线
var EmployeeList []Employee   // ❌ 不要首字母大写（除非是导出的）
var userid string            // ❌ ID应该大写
```

### 5. 常量名 (Constant Names)

- 导出常量：使用 `PascalCase`
- 私有常量：使用 `camelCase`
- API端点常量：使用 `PascalCase` + `Key` 后缀

```go
const (
    GetEmployeeListKey = "/topapi/user/listsimple"  // ✅ API端点
    SendMessageKey     = "/message/send"            // ✅ API端点
    DefaultTimeout     = 30                         // ✅ 导出常量
    maxRetryCount      = 3                          // ✅ 私有常量
)

const (
    GET_EMPLOYEE_LIST_KEY = "/api/users"  // ❌ 不要使用下划线
    getEmployeeListKey    = "/api/users"  // ❌ API端点应该导出
)
```

### 6. MCP工具名 (MCP Tool Names)

- 使用 `snake_case`
- 小写字母和下划线
- 动词开头，描述清晰的操作

```go
"get_employee_list"           // ✅
"send_corp_conversation"      // ✅
"recall_corp_conversation"    // ✅

"GetEmployeeList"             // ❌ 不要使用PascalCase
"get-employee-list"           // ❌ 不要使用连字符
"getEmployeeList"             // ❌ 不要使用camelCase
```

## 结构体设计规范

### 1. 服务结构体模式

```go
type EmployeeService struct {
    client *dingtalk.DingTalk
}

func NewEmployeeService(client *dingtalk.DingTalk) *EmployeeService {
    return &EmployeeService{client: client}
}

func (emp *EmployeeService) AddTools(svc *server.MCPServer) {
    // 工具注册逻辑
}
```

### 2. 响应结构体模式

```go
type GetEmployeeListResponse struct {
    Response
    Result struct {
        HasMore    bool          `json:"has_more"`
        NextCursor int           `json:"next_cursor"`
        List       []Employee    `json:"list"`
    } `json:"result"`
}
```

### 3. 请求结构体模式

```go
type SendMessageRequest struct {
    AgentID    int    `json:"agent_id"`
    UserIDList string `json:"userid_list"`
    Message    Message `json:"msg"`
}
```

## 方法签名规范

### 1. MCP工具处理方法

```go
func (svc *ServiceName) ToolMethodName(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
    // 方法实现
}
```

### 2. 钉钉API调用方法

```go
func (ds *DingTalk) ApiMethodName(param1 Type1, param2 Type2) (*response.ResponseType, error) {
    // 方法实现
}
```

### 3. 构造函数

```go
func NewServiceName(dependencies...) *ServiceName {
    return &ServiceName{
        // 初始化字段
    }
}
```

## 注释规范

### 1. 包注释

```go
/*
Package service provides MCP tool implementations for DingTalk API integration.

This package contains service layer implementations that handle MCP tool calls
and interact with the DingTalk client to perform various operations.
*/
package service
```

### 2. 类型注释

```go
// EmployeeService 员工服务，提供员工相关的MCP工具
// 包括获取员工列表、员工详情等功能
type EmployeeService struct {
    client *dingtalk.DingTalk
}
```

### 3. 方法注释

```go
// GetEmployeeList 获取企业员工列表
// 支持分页查询，返回员工的基础信息包括姓名和用户ID
func (emp *EmployeeService) GetEmployeeList(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
    // 方法实现
}
```

### 4. 复杂逻辑注释

```go
func (ds *DingTalk) processRequest() error {
    // 1. 验证请求参数
    if err := validateParams(); err != nil {
        return err
    }
    
    // 2. 获取访问令牌，如果缓存中没有则重新获取
    token, err := ds.getAccessToken()
    if err != nil {
        return err
    }
    
    // 3. 发送HTTP请求到钉钉API
    return ds.sendHTTPRequest(token)
}
```

## 错误处理规范

### 1. 错误信息格式

```go
// ✅ 提供上下文信息的错误
return nil, fmt.Errorf("failed to get employee list: %w", err)

// ✅ 自定义错误信息
return nil, errors.New("employee ID cannot be empty")

// ❌ 不提供上下文的错误
return nil, err
```

### 2. 参数验证错误

```go
func (svc *EmployeeService) GetEmployeeInfo(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
    // 类型断言安全检查
    userID, ok := req.Params.Arguments["user_id"].(string)
    if !ok {
        return nil, errors.New("user_id must be a string")
    }
    
    // 业务逻辑验证
    if userID == "" {
        return nil, errors.New("user_id cannot be empty")
    }
    
    // 继续处理...
}
```

## JSON标签规范

### 1. 标签格式

```go
type Employee struct {
    UserID   string `json:"userid"`           // ✅ 小写+下划线
    Name     string `json:"name"`             // ✅ 简单字段
    DeptID   int    `json:"dept_id"`          // ✅ 下划线分隔
    Mobile   string `json:"mobile,omitempty"` // ✅ 可选字段
}
```

### 2. 特殊标签

```go
type Response struct {
    Code    int    `json:"errcode"`           // 钉钉API返回字段
    Message string `json:"errmsg,omitempty"`  // 可选字段
    Result  bool   `json:"result,omitempty"`  // 可选字段
    Data    []byte `json:"-"`                 // 忽略字段
}
```

## 导入语句规范

### 1. 导入顺序

```go
import (
    // 1. 标准库
    "context"
    "encoding/json"
    "fmt"
    "net/http"
    
    // 2. 第三方库
    "github.com/mark3labs/mcp-go/mcp"
    "github.com/mark3labs/mcp-go/server"
    
    // 3. 本项目包
    "github.com/zhaoyunxing92/dingtalk-mcp/pkg/dingtalk"
    "github.com/zhaoyunxing92/dingtalk-mcp/pkg/dingtalk/constant"
)
```

### 2. 别名使用

```go
import (
    "context"
    
    mcp "github.com/mark3labs/mcp-go/mcp"        // ✅ 合理的别名
    "github.com/zhaoyunxing92/dingtalk-mcp/pkg/dingtalk/response"
)
```

遵循这些规范将确保代码库的一致性和可维护性，使得团队协作更加高效。