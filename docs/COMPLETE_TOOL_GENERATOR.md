# 钉钉MCP工具完整生成模板

## 使用说明

这是一个完整的提示词模板，用于根据钉钉API文档自动生成符合项目标准的MCP工具。使用时请将 `{API_DOCUMENTATION}` 替换为具体的API文档内容。

---

# 钉钉MCP工具自动生成请求

请根据以下钉钉API文档，为钉钉MCP项目生成一个完整、标准、可直接使用的MCP工具实现。

## 项目架构信息

### 目录结构
```
dingtalk-mcp/
├── main.go                          # 程序入口
├── internal/service/                # MCP服务层
├── pkg/dingtalk/                    # 钉钉客户端封装
│   ├── dingtakl.go                  # 主客户端
│   ├── constant/api.go              # API端点常量
│   ├── response/                    # 响应结构
│   └── models/                      # 数据模型
```

### 现有导入依赖
```go
import (
    "context"
    "encoding/json"
    "net/http"
    "strconv"
    
    "github.com/mark3labs/mcp-go/mcp"
    "github.com/mark3labs/mcp-go/server"
    "github.com/pkg/errors"
    
    "github.com/zhaoyunxing92/dingtalk-mcp/pkg/dingtalk"
    "github.com/zhaoyunxing92/dingtalk-mcp/pkg/dingtalk/constant"
    "github.com/zhaoyunxing92/dingtalk-mcp/pkg/dingtalk/response"
)
```

### 统一的响应基类
```go
type Response struct {
    Code      int    `json:"errcode"`
    Msg       string `json:"errmsg,omitempty"`
    Success   bool   `json:"success,omitempty"`
    RequestId string `json:"request_id,omitempty"`
    TraceId   string `json:"requestId,omitempty"`
    Result    bool   `json:"result,omitempty"`
}

func (res *Response) CheckError(data []byte) error {
    if res.Code == 0 {
        return nil
    }
    return errors.New(string(data))
}
```

### 钉钉客户端调用模式
```go
func (ds *DingTalk) Request(method, path string, query url.Values, body interface{}, data response.Unmarshalled) error {
    // 自动处理access_token、新旧API版本区分、错误检查等
}
```

## 严格的编码规范

### 命名规范（必须严格遵循）
- **常量名**：`YourApiEndpointKey` (PascalCase + Key后缀)
- **结构体名**：`YourApiResponse`, `YourService` (PascalCase)
- **方法名**：`YourApiMethod`, `GetSomething` (PascalCase)
- **文件名**：`your_service.go` (snake_case)
- **MCP工具名**：`your_tool_name` (snake_case)
- **变量名**：`userID`, `apiKey` (camelCase，缩写保持大写)

### 必需的代码结构模板

**1. 常量定义（添加到 `pkg/dingtalk/constant/api.go`）**
```go
const (
    YourApiEndpointKey = "/your/api/endpoint" // 功能描述
)
```

**2. 响应结构（创建新文件或添加到现有响应文件）**
```go
package response

type YourApiResponse struct {
    Response
    Result YourResultStruct `json:"result"`
}

type YourResultStruct struct {
    Field1 string `json:"field1"`
    Field2 int    `json:"field2"`
    // 根据API文档完整定义
}
```

**3. 客户端方法（添加到相应的pkg/dingtalk文件）**
```go
// YourApiMethod API功能描述
// 参数说明和使用注意事项
func (ds *DingTalk) YourApiMethod(param1 string, param2 int) (*response.YourApiResponse, error) {
    var (
        body = map[string]interface{}{
            "param1": param1,
            "param2": param2,
        }
        data = &response.YourApiResponse{}
        err  error
    )
    
    if err = ds.Request(http.MethodPost, constant.YourApiEndpointKey, nil, body, data); err != nil {
        return nil, err
    }
    
    return data, nil
}
```

**4. 服务层实现（创建或扩展service文件）**
```go
package service

import (
    "context"
    "encoding/json"
    
    "github.com/mark3labs/mcp-go/mcp"
    "github.com/mark3labs/mcp-go/server"
    
    "github.com/zhaoyunxing92/dingtalk-mcp/pkg/dingtalk"
)

type YourService struct {
    client *dingtalk.DingTalk
}

func NewYourService(client *dingtalk.DingTalk) *YourService {
    return &YourService{client: client}
}

// YourToolMethod 工具功能的中文描述
func (svc *YourService) YourToolMethod(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
    // 1. 安全的参数提取和验证
    param1, ok := req.Params.Arguments["param1"].(string)
    if !ok {
        return nil, errors.New("param1必须是字符串类型")
    }
    
    param2, ok := req.Params.Arguments["param2"].(int)
    if !ok {
        return nil, errors.New("param2必须是整数类型")
    }
    
    // 2. 参数业务逻辑验证
    if param1 == "" {
        return nil, errors.New("param1不能为空")
    }
    
    // 3. 调用钉钉API
    resp, err := svc.client.YourApiMethod(param1, param2)
    if err != nil {
        return nil, fmt.Errorf("调用钉钉API失败: %w", err)
    }
    
    // 4. 格式化响应
    if marshal, err := json.Marshal(resp.Result); err != nil {
        return nil, fmt.Errorf("响应数据序列化失败: %w", err)
    } else {
        return mcp.NewToolResultText(string(marshal)), nil
    }
}

// AddTools 注册MCP工具
func (svc *YourService) AddTools(server *server.MCPServer) {
    yourTool := mcp.NewTool("your_tool_name",
        mcp.WithDescription("工具功能的详细中文描述，包括用途和使用场景"),
        mcp.WithString("param1",
            mcp.Required(),
            mcp.Description("参数1的详细中文描述，包括格式要求和限制")),
        mcp.WithInt("param2",
            mcp.Required(),
            mcp.Description("参数2的详细中文描述，包括取值范围和含义")))
    
    server.AddTool(yourTool, svc.YourToolMethod)
}
```

**5. 主程序注册（添加到main.go）**
```go
// 在main函数中添加以下行
service.NewYourService(client).AddTools(svc)
```

## 必须处理的特殊情况

### 1. 可选参数处理
```go
// 如果API有可选参数，使用以下模式
func (svc *YourService) YourToolMethod(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
    // 必需参数
    required := req.Params.Arguments["required"].(string)
    
    // 可选参数处理
    var optional *int
    if val, ok := req.Params.Arguments["optional"].(int); ok {
        optional = &val
    }
    
    // 调用API时处理可选参数
    resp, err := svc.client.YourApiMethod(required, optional)
    // ...
}

// MCP工具定义中不使用Required()
mcp.WithInt("optional",
    mcp.Description("可选参数描述，如不提供则使用默认值"))
```

### 2. 分页处理
```go
// 如果API支持分页，实现以下模式
func (svc *YourService) GetPagedData(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
    cursor, _ := req.Params.Arguments["cursor"].(int) // 默认为0
    size, _ := req.Params.Arguments["size"].(int)     // 默认为100
    
    if size <= 0 || size > 100 {
        size = 100 // 限制最大页面大小
    }
    
    resp, err := svc.client.GetPagedData(cursor, size)
    if err != nil {
        return nil, err
    }
    
    // 包含分页信息的响应
    result := map[string]interface{}{
        "data":        resp.Result.List,
        "has_more":    resp.Result.HasMore,
        "next_cursor": resp.Result.NextCursor,
        "total_count": len(resp.Result.List),
    }
    
    marshal, _ := json.Marshal(result)
    return mcp.NewToolResultText(string(marshal)), nil
}
```

### 3. 批量操作
```go
// 如果是批量操作，添加适当的限制
func (svc *YourService) BatchOperation(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
    items := req.Params.Arguments["items"].(string) // 逗号分隔的列表
    
    itemList := strings.Split(items, ",")
    if len(itemList) > 100 {
        return nil, errors.New("批量操作最多支持100个项目")
    }
    
    // 处理批量逻辑
}
```

## API文档
```
{API_DOCUMENTATION}
```

## 生成要求

请根据上述API文档，严格按照模板生成以下完整代码：

1. **常量定义**：API端点常量，添加到 `pkg/dingtalk/constant/api.go`
2. **响应结构**：完整的响应数据结构定义
3. **客户端方法**：钉钉API调用封装，包含完整的错误处理
4. **服务实现**：MCP工具服务，包含参数验证和错误处理
5. **工具注册**：MCP工具定义和注册代码
6. **主程序集成**：服务注册代码片段

## 质量要求

- ✅ 所有代码必须包含完整的中文注释
- ✅ 必须处理所有可能的错误情况
- ✅ 参数验证必须全面且提供友好的中文错误信息
- ✅ 工具描述必须详细说明功能、用途和使用场景
- ✅ 代码必须遵循项目的命名规范
- ✅ 响应结构必须完整映射API文档中的所有字段
- ✅ 必须正确处理可选参数、分页、批量等特殊情况
- ✅ 工具名称必须在项目中唯一

## 输出格式

请按以下格式提供完整代码：

### 文件1: 常量定义 (pkg/dingtalk/constant/api.go 新增内容)
```go
// 在现有常量中添加
const (
    YourApiEndpointKey = "/your/endpoint" // 功能描述
)
```

### 文件2: 响应结构 (pkg/dingtalk/response/your_feature.go)
```go
// 完整的文件内容
```

### 文件3: 客户端方法 (pkg/dingtalk/your_feature.go)
```go
// 完整的文件内容
```

### 文件4: 服务实现 (internal/service/your_service.go)
```go
// 完整的文件内容
```

### 文件5: 主程序集成 (main.go 修改)
```go
// 需要在main.go中添加的行
service.NewYourService(client).AddTools(svc)
```

请确保生成的代码可以直接使用，无需进一步修改。