# 钉钉MCP工具开发最佳实践指南

## 概述

本文档提供了在钉钉MCP项目中开发工具时的最佳实践，包含常见场景的处理方法、性能优化建议和问题解决方案。

## 参数处理最佳实践

### 1. 安全的类型断言

```go
// ✅ 推荐：使用安全的类型断言
func (svc *Service) HandleTool(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
    userID, ok := req.Params.Arguments["user_id"].(string)
    if !ok {
        return nil, fmt.Errorf("user_id must be a string, got %T", req.Params.Arguments["user_id"])
    }
    
    if userID == "" {
        return nil, errors.New("user_id cannot be empty")
    }
    
    // 继续处理...
}

// ❌ 避免：直接类型断言可能导致panic
func (svc *Service) HandleTool(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
    userID := req.Params.Arguments["user_id"].(string) // 可能panic
    // ...
}
```

### 2. 可选参数处理

```go
// ✅ 推荐：使用指针类型处理可选参数
func (ds *DingTalk) GetEmployeeList(deptID int, cursor *int, size *int) (*response.ListUserSimpleResponse, error) {
    body := map[string]interface{}{
        "dept_id": deptID,
    }
    
    // 设置可选参数的默认值
    if cursor != nil {
        body["cursor"] = *cursor
    } else {
        body["cursor"] = 0
    }
    
    if size != nil {
        body["size"] = *size
    } else {
        body["size"] = 100 // 默认值
    }
    
    // 继续处理...
}

// ✅ 推荐：MCP层的可选参数处理
func (svc *EmployeeService) GetEmployeeList(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
    deptID := req.Params.Arguments["dept_id"].(int)
    
    var cursor, size *int
    if c, ok := req.Params.Arguments["cursor"].(int); ok {
        cursor = &c
    }
    if s, ok := req.Params.Arguments["size"].(int); ok {
        size = &s
    }
    
    resp, err := svc.client.GetEmployeeList(deptID, cursor, size)
    // ...
}
```

### 3. 参数验证模式

```go
// ✅ 推荐：集中的参数验证
func (svc *MessageService) validateSendMessageParams(req mcp.CallToolRequest) (string, string, error) {
    userIDs, ok := req.Params.Arguments["user_ids"].(string)
    if !ok {
        return "", "", errors.New("user_ids must be a string")
    }
    
    content, ok := req.Params.Arguments["content"].(string)
    if !ok {
        return "", "", errors.New("content must be a string")
    }
    
    // 业务逻辑验证
    if userIDs == "" {
        return "", "", errors.New("user_ids cannot be empty")
    }
    
    if len(content) > 2048 {
        return "", "", errors.New("content length cannot exceed 2048 characters")
    }
    
    // 验证用户ID格式
    if !isValidUserIDList(userIDs) {
        return "", "", errors.New("user_ids format is invalid, should be comma-separated")
    }
    
    return userIDs, content, nil
}

func isValidUserIDList(userIDs string) bool {
    // 检查是否为有效的用户ID列表格式
    parts := strings.Split(userIDs, ",")
    if len(parts) > 100 {
        return false // 钉钉限制最多100个用户
    }
    
    for _, part := range parts {
        if strings.TrimSpace(part) == "" {
            return false
        }
    }
    
    return true
}
```

## 错误处理最佳实践

### 1. 分层错误处理

```go
// Client层：包装底层错误
func (ds *DingTalk) GetEmployeeInfo(userID string) (*response.EmployeeResponse, error) {
    if userID == "" {
        return nil, errors.New("user ID is required")
    }
    
    body := map[string]interface{}{"userid": userID}
    data := &response.EmployeeResponse{}
    
    if err := ds.Request(http.MethodPost, constant.GetUserDetailKey, nil, body, data); err != nil {
        return nil, fmt.Errorf("failed to get employee info for user %s: %w", userID, err)
    }
    
    return data, nil
}

// Service层：处理业务逻辑错误
func (svc *EmployeeService) GetEmployeeInfo(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
    userID, ok := req.Params.Arguments["user_id"].(string)
    if !ok {
        return nil, errors.New("invalid parameter: user_id must be a string")
    }
    
    resp, err := svc.client.GetEmployeeInfo(userID)
    if err != nil {
        // 根据错误类型返回用户友好的消息
        if strings.Contains(err.Error(), "user not found") {
            return nil, fmt.Errorf("employee not found: %s", userID)
        }
        return nil, fmt.Errorf("failed to retrieve employee information: %w", err)
    }
    
    result, _ := json.Marshal(resp.Result)
    return mcp.NewToolResultText(string(result)), nil
}
```

### 2. 错误信息本地化

```go
var (
    ErrUserNotFound     = errors.New("用户不存在")
    ErrPermissionDenied = errors.New("权限不足")
    ErrRateLimitExceeded = errors.New("请求频率超限，请稍后重试")
    ErrInvalidParameter = errors.New("参数格式错误")
)

func (svc *Service) handleDingTalkError(err error) error {
    if err == nil {
        return nil
    }
    
    // 根据钉钉错误码转换为用户友好的消息
    errStr := err.Error()
    switch {
    case strings.Contains(errStr, "60011"):
        return ErrUserNotFound
    case strings.Contains(errStr, "60004"):
        return ErrPermissionDenied
    case strings.Contains(errStr, "90018"):
        return ErrRateLimitExceeded
    default:
        return fmt.Errorf("钉钉API调用失败: %w", err)
    }
}
```

## 性能优化最佳实践

### 1. 缓存使用

```go
// ✅ 推荐：利用现有的缓存机制
func (ds *DingTalk) GetDepartmentInfo(deptID int) (*response.DepartmentResponse, error) {
    cacheKey := fmt.Sprintf("dept_%d", deptID)
    
    // 尝试从缓存获取
    if cached, err := ds.cache.Get(cacheKey); err == nil {
        var dept response.DepartmentResponse
        if json.Unmarshal([]byte(cached), &dept) == nil {
            return &dept, nil
        }
    }
    
    // 缓存未命中，调用API
    resp, err := ds.callDepartmentAPI(deptID)
    if err != nil {
        return nil, err
    }
    
    // 缓存结果（5分钟有效期）
    if data, err := json.Marshal(resp); err == nil {
        ds.cache.SetWithTTL(cacheKey, string(data), 5*time.Minute)
    }
    
    return resp, nil
}
```

### 2. 批量操作优化

```go
// ✅ 推荐：支持批量操作
func (svc *EmployeeService) GetMultipleEmployees(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
    userIDsStr, ok := req.Params.Arguments["user_ids"].(string)
    if !ok {
        return nil, errors.New("user_ids must be a string")
    }
    
    userIDs := strings.Split(userIDsStr, ",")
    if len(userIDs) > 50 { // 限制批量大小
        return nil, errors.New("maximum 50 users allowed per request")
    }
    
    // 并发获取用户信息
    results := make([]interface{}, len(userIDs))
    errChan := make(chan error, len(userIDs))
    
    for i, userID := range userIDs {
        go func(index int, id string) {
            resp, err := svc.client.GetEmployeeInfo(strings.TrimSpace(id))
            if err != nil {
                errChan <- fmt.Errorf("failed to get user %s: %w", id, err)
                return
            }
            results[index] = resp.Result
            errChan <- nil
        }(i, userID)
    }
    
    // 等待所有goroutine完成
    for i := 0; i < len(userIDs); i++ {
        if err := <-errChan; err != nil {
            return nil, err
        }
    }
    
    result, _ := json.Marshal(results)
    return mcp.NewToolResultText(string(result)), nil
}
```

### 3. 分页处理优化

```go
// ✅ 推荐：智能分页处理
func (svc *EmployeeService) GetAllEmployees(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
    deptID := req.Params.Arguments["dept_id"].(int)
    
    var allEmployees []response.SimpleEmployee
    cursor := 0
    pageSize := 100
    
    for {
        resp, err := svc.client.GetSimpleEmployees(deptID, cursor, pageSize)
        if err != nil {
            return nil, fmt.Errorf("failed to get employees at cursor %d: %w", cursor, err)
        }
        
        allEmployees = append(allEmployees, resp.Result.List...)
        
        if !resp.Result.HasMore {
            break
        }
        
        cursor = resp.Result.NextCursor
        
        // 防止无限循环
        if len(allEmployees) > 10000 {
            return nil, errors.New("too many employees, consider using pagination")
        }
    }
    
    result, _ := json.Marshal(allEmployees)
    return mcp.NewToolResultText(string(result)), nil
}
```

## 数据格式化最佳实践

### 1. 响应格式标准化

```go
// ✅ 推荐：结构化的响应格式
func (svc *EmployeeService) formatEmployeeResponse(employees []response.SimpleEmployee) map[string]interface{} {
    return map[string]interface{}{
        "total_count": len(employees),
        "employees": employees,
        "timestamp": time.Now().Unix(),
    }
}

func (svc *EmployeeService) GetEmployeeList(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
    // ... 获取数据逻辑 ...
    
    formatted := svc.formatEmployeeResponse(resp.Result.List)
    result, _ := json.Marshal(formatted)
    return mcp.NewToolResultText(string(result)), nil
}
```

### 2. 错误响应标准化

```go
// ✅ 推荐：统一的错误响应格式
type ErrorResponse struct {
    Error     string `json:"error"`
    Code      string `json:"code,omitempty"`
    Details   string `json:"details,omitempty"`
    Timestamp int64  `json:"timestamp"`
}

func (svc *Service) formatError(err error, code string) *mcp.CallToolResult {
    errorResp := ErrorResponse{
        Error:     err.Error(),
        Code:      code,
        Timestamp: time.Now().Unix(),
    }
    
    result, _ := json.Marshal(errorResp)
    return mcp.NewToolResultText(string(result))
}
```

## 测试最佳实践

### 1. 单元测试结构

```go
func TestEmployeeService_GetEmployeeList(t *testing.T) {
    tests := []struct {
        name          string
        mockResponse  *response.ListUserSimpleResponse
        mockError     error
        expectedError string
    }{
        {
            name: "successful_request",
            mockResponse: &response.ListUserSimpleResponse{
                Response: response.Response{Code: 0},
                Result: struct {
                    HasMore    bool                      `json:"has_more"`
                    NextCursor int                       `json:"next_cursor"`
                    List       []response.SimpleEmployee `json:"list"`
                }{
                    List: []response.SimpleEmployee{
                        {UserID: "user1", Name: "张三"},
                        {UserID: "user2", Name: "李四"},
                    },
                },
            },
        },
        {
            name:          "api_error",
            mockError:     errors.New("API error"),
            expectedError: "API error",
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            // 测试实现
        })
    }
}
```

### 2. 集成测试

```go
func TestEmployeeService_Integration(t *testing.T) {
    if testing.Short() {
        t.Skip("skipping integration test")
    }
    
    // 使用测试配置
    client := dingtalk.NewClient(
        testAgentID,
        testAppKey,
        testAppSecret,
    )
    
    service := NewEmployeeService(client)
    
    // 构造测试请求
    req := mcp.CallToolRequest{
        Params: mcp.CallToolRequestParams{
            Arguments: map[string]interface{}{
                "dept_id": 1,
            },
        },
    }
    
    result, err := service.GetEmployeeList(context.Background(), req)
    assert.NoError(t, err)
    assert.NotNil(t, result)
}
```

## 安全最佳实践

### 1. 敏感信息处理

```go
// ✅ 推荐：过滤敏感信息
func (svc *EmployeeService) sanitizeEmployeeData(employee response.DetailEmployee) map[string]interface{} {
    return map[string]interface{}{
        "user_id":   employee.UserID,
        "name":      employee.Name,
        "dept_id":   employee.DeptID,
        // 不包含手机号、邮箱等敏感信息
    }
}
```

### 2. 参数校验

```go
// ✅ 推荐：严格的参数校验
func validateUserID(userID string) error {
    if userID == "" {
        return errors.New("user_id is required")
    }
    
    if len(userID) > 64 {
        return errors.New("user_id too long")
    }
    
    // 检查是否包含非法字符
    if matched, _ := regexp.MatchString(`^[a-zA-Z0-9_-]+$`, userID); !matched {
        return errors.New("user_id contains invalid characters")
    }
    
    return nil
}
```

## 调试和监控

### 1. 日志记录

```go
// ✅ 推荐：结构化日志
func (svc *EmployeeService) GetEmployeeList(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
    deptID := req.Params.Arguments["dept_id"].(int)
    
    log.Printf("GetEmployeeList called with dept_id=%d", deptID)
    
    start := time.Now()
    resp, err := svc.client.GetSimpleEmployees(deptID, 0, 100)
    duration := time.Since(start)
    
    if err != nil {
        log.Printf("GetEmployeeList failed: dept_id=%d, error=%v, duration=%v", deptID, err, duration)
        return nil, err
    }
    
    log.Printf("GetEmployeeList success: dept_id=%d, count=%d, duration=%v", deptID, len(resp.Result.List), duration)
    
    result, _ := json.Marshal(resp.Result.List)
    return mcp.NewToolResultText(string(result)), nil
}
```

### 2. 性能监控

```go
// ✅ 推荐：性能监控装饰器
func withPerformanceMonitoring(toolName string, handler func(context.Context, mcp.CallToolRequest) (*mcp.CallToolResult, error)) func(context.Context, mcp.CallToolRequest) (*mcp.CallToolResult, error) {
    return func(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
        start := time.Now()
        
        result, err := handler(ctx, req)
        
        duration := time.Since(start)
        if duration > 5*time.Second {
            log.Printf("SLOW_QUERY: tool=%s, duration=%v", toolName, duration)
        }
        
        return result, err
    }
}
```

遵循这些最佳实践将帮助你开发出高质量、可维护的钉钉MCP工具。