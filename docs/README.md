# 钉钉MCP项目开发文档

## 文档概述

本目录包含了钉钉MCP项目的完整开发文档体系，为开发者提供从项目理解到工具开发的全套指导。

## 文档结构

### 📋 [DEVELOPMENT_GUIDE.md](./DEVELOPMENT_GUIDE.md)
**标准化开发流程指南**
- 项目核心架构分析
- 详细的开发流程步骤
- 代码实现模式说明
- 质量检查清单
- 常见模式和最佳实践

**适用场景**: 初次接触项目的开发者，需要了解整体架构和开发流程

### 🛠️ [CODE_STANDARDS.md](./CODE_STANDARDS.md)
**代码结构和命名规范**
- 目录结构规范
- 详细的命名规范（包、类型、方法、常量、变量）
- 结构体设计模式
- 方法签名规范
- 注释和JSON标签规范
- 导入语句规范

**适用场景**: 需要了解具体编码规范的开发者

### ⚡ [BEST_PRACTICES.md](./BEST_PRACTICES.md)
**开发最佳实践指南**
- 参数处理最佳实践
- 错误处理模式
- 性能优化建议
- 数据格式化标准
- 测试策略
- 安全最佳实践
- 调试和监控方法

**适用场景**: 希望编写高质量、高性能代码的开发者

### 🔧 [TOOL_GENERATOR_PROMPT.md](./TOOL_GENERATOR_PROMPT.md)
**基础工具生成模板**
- 项目结构约定说明
- 基础代码模式模板
- 必须遵循的规范
- 简化的使用说明

**适用场景**: 有一定项目经验，需要快速生成工具的开发者

### 🚀 [COMPLETE_TOOL_GENERATOR.md](./COMPLETE_TOOL_GENERATOR.md)
**完整工具生成模板（推荐使用）**
- 详细的项目架构信息
- 严格的编码规范要求
- 完整的代码结构模板
- 特殊情况处理指导
- 质量要求检查
- 结构化的输出格式

**适用场景**: 需要生成完整、标准、可直接使用工具的所有开发者

## 使用指南

### 1. 新手开发者
建议按以下顺序阅读文档：
1. `DEVELOPMENT_GUIDE.md` - 了解项目架构和流程
2. `CODE_STANDARDS.md` - 学习编码规范
3. `BEST_PRACTICES.md` - 掌握最佳实践
4. `COMPLETE_TOOL_GENERATOR.md` - 使用完整模板开发工具

### 2. 有经验的开发者
可以直接使用：
- `COMPLETE_TOOL_GENERATOR.md` - 快速生成工具
- `BEST_PRACTICES.md` - 参考最佳实践

### 3. 项目维护者
参考文档体系：
- `CODE_STANDARDS.md` - 代码审查标准
- `BEST_PRACTICES.md` - 质量要求
- `DEVELOPMENT_GUIDE.md` - 新成员培训材料

## 工具生成流程

### 快速开始
1. 获取钉钉API文档
2. 使用 `COMPLETE_TOOL_GENERATOR.md` 中的提示词模板
3. 将API文档内容替换模板中的 `{API_DOCUMENTATION}` 占位符
4. 使用AI工具（如Claude）生成完整代码
5. 按照生成的代码文件结构添加到项目中

### 示例流程
```bash
# 1. 准备API文档
api_doc="钉钉API文档内容..."

# 2. 使用模板生成代码
prompt=$(cat docs/COMPLETE_TOOL_GENERATOR.md | sed "s/{API_DOCUMENTATION}/$api_doc/g")

# 3. 将生成的代码添加到对应文件
# 4. 在main.go中注册新服务
# 5. 构建和测试
```

## 质量保证

### 代码质量检查清单
根据文档生成的代码应该满足以下要求：

**基础要求**
- [ ] 遵循项目命名规范
- [ ] 包含完整的中文注释
- [ ] 实现了所有必需的错误处理
- [ ] 参数验证完整且友好

**功能要求**
- [ ] API常量正确定义
- [ ] 响应结构完整映射
- [ ] 客户端方法实现正确
- [ ] MCP工具正确注册
- [ ] 能够成功构建项目

**高级要求**
- [ ] 性能优化考虑
- [ ] 安全性检查
- [ ] 可测试性设计
- [ ] 监控和日志支持

## 持续改进

### 文档维护
- 随着项目演进，及时更新文档内容
- 收集开发者反馈，优化模板和指南
- 增加新的最佳实践和模式

### 模板优化
- 根据实际使用情况优化提示词模板
- 增加对新API类型的支持
- 改进错误处理和边界情况处理

## 支持和反馈

如果在使用这些文档时遇到问题，请：
1. 检查相关的最佳实践文档
2. 参考现有工具的实现
3. 在项目中创建issue报告问题
4. 贡献改进建议和补充文档

---

这套文档体系确保了钉钉MCP项目的开发标准化和质量一致性，使得任何开发者都能快速上手并开发出高质量的工具。