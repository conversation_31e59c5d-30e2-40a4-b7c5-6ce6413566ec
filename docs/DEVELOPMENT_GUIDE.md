# 钉钉MCP工具标准化开发指南

## 项目概述

本文档提供了钉钉MCP项目中开发新工具的标准化流程，确保代码质量、一致性和可维护性。

## 核心架构分析

### 1. 分层架构模式

项目采用清晰的分层架构：

```
internal/service/     # MCP服务层：处理MCP工具调用
pkg/dingtalk/        # 钉钉客户端层：封装API调用
pkg/dingtalk/models/ # 数据模型层：请求/响应结构
pkg/dingtalk/constant/ # 常量层：API端点定义
pkg/dingtalk/response/ # 响应层：通用响应结构
```

### 2. MCP工具注册机制

每个服务通过 `AddTools(svc *server.MCPServer)` 方法注册工具：
- 使用 `mcp.NewTool()` 创建工具定义
- 通过 `svc.AddTool()` 注册工具和处理函数
- 工具处理函数签名：`func(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error)`

### 3. 钉钉API集成模式

- **统一请求方法**：`ds.Request(method, path, query, body, response)`
- **自动Token管理**：通过缓存机制自动处理access_token
- **错误处理**：统一的响应结构和错误检查
- **API版本支持**：自动区分新旧API版本

## 标准开发流程

### 阶段1：API文档分析

1. **识别API类型**
   - 查询类API（如获取用户列表）
   - 操作类API（如发送消息）
   - 管理类API（如创建/删除资源）

2. **分析请求参数**
   - 必需参数 vs 可选参数
   - 参数类型和验证规则
   - 默认值和限制条件

3. **分析响应结构**
   - 成功响应的数据结构
   - 错误码和错误信息
   - 分页机制（如适用）

### 阶段2：代码结构设计

#### 2.1 常量定义
在 `pkg/dingtalk/constant/api.go` 中添加API端点：

```go
const (
    NewApiEndpointKey = "/path/to/api" // API描述
)
```

#### 2.2 响应结构定义
在 `pkg/dingtalk/response/` 中创建响应结构：

```go
type YourApiResponse struct {
    Response
    Result YourResultStruct `json:"result"`
}

type YourResultStruct struct {
    // 根据API文档定义字段
    Field1 string `json:"field1"`
    Field2 int    `json:"field2"`
}
```

#### 2.3 客户端方法实现
在相应的 `pkg/dingtalk/` 文件中实现API调用：

```go
func (ds *DingTalk) YourApiMethod(param1 string, param2 int) (*response.YourApiResponse, error) {
    var (
        body = map[string]interface{}{
            "param1": param1,
            "param2": param2,
        }
        data = &response.YourApiResponse{}
        err  error
    )
    
    if err = ds.Request(http.MethodPost, constant.NewApiEndpointKey, nil, body, data); err != nil {
        return nil, err
    }
    
    return data, nil
}
```

#### 2.4 服务层实现
在 `internal/service/` 中创建或扩展服务：

```go
// MCP工具处理函数
func (svc *YourService) YourToolMethod(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
    // 1. 参数提取和验证
    param1 := req.Params.Arguments["param1"].(string)
    param2 := req.Params.Arguments["param2"].(int)
    
    // 2. 调用钉钉API
    resp, err := svc.client.YourApiMethod(param1, param2)
    if err != nil {
        return nil, err
    }
    
    // 3. 格式化响应
    if marshal, err := json.Marshal(resp.Result); err != nil {
        return nil, err
    } else {
        return mcp.NewToolResultText(string(marshal)), nil
    }
}

// 工具注册方法
func (svc *YourService) AddTools(server *server.MCPServer) {
    yourTool := mcp.NewTool("your_tool_name",
        mcp.WithDescription("工具功能描述"),
        mcp.WithString("param1",
            mcp.Required(),
            mcp.Description("参数1描述")),
        mcp.WithInt("param2",
            mcp.Required(),
            mcp.Description("参数2描述")))
    
    server.AddTool(yourTool, svc.YourToolMethod)
}
```

### 阶段3：集成和注册

在 `main.go` 中注册新服务：

```go
func main() {
    // ... 现有代码 ...
    
    service.NewYourService(client).AddTools(svc)
    
    // ... 现有代码 ...
}
```

## 编码规范

### 1. 命名规范

- **文件命名**：小写+下划线，如 `your_service.go`
- **结构体**：PascalCase，如 `YourService`
- **方法名**：PascalCase，如 `YourApiMethod`
- **常量**：大写+下划线，如 `YOUR_API_KEY`
- **MCP工具名**：小写+下划线，如 `your_tool_name`

### 2. 注释规范

```go
// YourApiMethod 方法功能描述
// 参数说明和使用注意事项
func (ds *DingTalk) YourApiMethod(param1 string) error {
    // 实现逻辑
}
```

### 3. 错误处理

- 使用统一的错误返回模式
- 不要忽略错误，始终进行适当处理
- 使用有意义的错误信息

### 4. 参数验证

```go
// MCP工具参数验证示例
func (svc *YourService) YourToolMethod(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
    // 类型断言时的安全检查
    param1, ok := req.Params.Arguments["param1"].(string)
    if !ok {
        return nil, errors.New("param1 must be a string")
    }
    
    // 业务逻辑验证
    if param1 == "" {
        return nil, errors.New("param1 cannot be empty")
    }
    
    // ... 继续处理
}
```

## 质量检查清单

### 开发完成后检查项：

- [ ] API常量已正确定义
- [ ] 响应结构完整且类型正确
- [ ] 客户端方法实现了完整的错误处理
- [ ] MCP工具参数定义清晰准确
- [ ] 工具描述信息完整
- [ ] 参数验证逻辑完备
- [ ] 错误信息有意义
- [ ] 代码注释完整
- [ ] 命名规范一致
- [ ] 已在main.go中注册服务

### 测试验证：

- [ ] 能够成功构建项目
- [ ] MCP工具能在Claude Desktop中正确识别
- [ ] 工具调用能返回预期结果
- [ ] 错误情况能正确处理
- [ ] 参数验证工作正常

## 常见模式和最佳实践

### 1. 分页处理模式

```go
func (ds *DingTalk) GetPagedData(cursor, size int) (*response.PagedResponse, error) {
    var (
        body = map[string]interface{}{
            "cursor": cursor,
            "size":   size,
        }
        data = &response.PagedResponse{}
    )
    
    if err := ds.Request(http.MethodPost, constant.PagedApiKey, nil, body, data); err != nil {
        return nil, err
    }
    
    return data, nil
}
```

### 2. 可选参数处理

```go
// 使用指针类型处理可选参数
func (ds *DingTalk) ApiWithOptionalParams(required string, optional *int) error {
    body := map[string]interface{}{
        "required": required,
    }
    
    if optional != nil {
        body["optional"] = *optional
    }
    
    // ... 继续处理
}
```

### 3. 批量操作模式

```go
func (ds *DingTalk) BatchOperation(items []string) (*response.BatchResponse, error) {
    // 检查批量限制
    if len(items) > 100 {
        return nil, errors.New("batch size cannot exceed 100")
    }
    
    // ... 实现批量逻辑
}
```

这个开发指南将确保所有新工具都遵循一致的模式和质量标准。