# 钉钉MCP工具自动生成提示词模板

## 使用说明

这个提示词模板可以直接用于生成新的钉钉MCP工具。只需要将 `{API_DOCUMENTATION}` 替换为具体的钉钉API文档内容，然后使用这个提示词即可自动生成完整的工具代码。

---

## 提示词模板

请根据以下钉钉API文档，为钉钉MCP项目生成一个完整的MCP工具。项目已有的架构和模式如下：

### 项目结构约定：
- `internal/service/` - MCP服务层，处理工具调用
- `pkg/dingtalk/` - 钉钉API客户端封装
- `pkg/dingtalk/constant/api.go` - API端点常量
- `pkg/dingtalk/response/` - API响应结构定义
- `pkg/dingtalk/models/` - 数据模型定义

### 现有代码模式：

**1. 常量定义模式：**
```go
const (
    YourApiEndpointKey = "/path/to/api" // API功能描述
)
```

**2. 响应结构模式：**
```go
type YourApiResponse struct {
    Response
    Result YourResultStruct `json:"result"`
}
```

**3. 客户端方法模式：**
```go
func (ds *DingTalk) YourApiMethod(param1 string, param2 int) (*response.YourApiResponse, error) {
    var (
        body = map[string]interface{}{
            "param1": param1,
            "param2": param2,
        }
        data = &response.YourApiResponse{}
        err  error
    )
    
    if err = ds.Request(http.MethodPost, constant.YourApiEndpointKey, nil, body, data); err != nil {
        return nil, err
    }
    
    return data, nil
}
```

**4. MCP服务层模式：**
```go
type YourService struct {
    client *dingtalk.DingTalk
}

func NewYourService(client *dingtalk.DingTalk) *YourService {
    return &YourService{client: client}
}

func (svc *YourService) YourToolMethod(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
    // 参数提取和验证
    param1 := req.Params.Arguments["param1"].(string)
    param2 := req.Params.Arguments["param2"].(int)
    
    // 调用钉钉API
    resp, err := svc.client.YourApiMethod(param1, param2)
    if err != nil {
        return nil, err
    }
    
    // 格式化返回结果
    if marshal, err := json.Marshal(resp.Result); err != nil {
        return nil, err
    } else {
        return mcp.NewToolResultText(string(marshal)), nil
    }
}

func (svc *YourService) AddTools(server *server.MCPServer) {
    yourTool := mcp.NewTool("your_tool_name",
        mcp.WithDescription("工具功能的中文描述"),
        mcp.WithString("param1",
            mcp.Required(),
            mcp.Description("参数1的中文描述和使用说明")),
        mcp.WithString("param2", 
            mcp.Required(),
            mcp.Description("参数2的中文描述和使用说明")))
    
    server.AddTool(yourTool, svc.YourToolMethod)
}
```

### 必须遵循的规范：

1. **命名规范**：
   - 文件名：snake_case（如 `user_service.go`）
   - 结构体：PascalCase（如 `UserService`）
   - 方法名：PascalCase（如 `GetUserInfo`）  
   - 常量：UPPER_SNAKE_CASE（如 `GET_USER_INFO_KEY`）
   - MCP工具名：snake_case（如 `get_user_info`）

2. **错误处理**：
   - 所有API调用必须检查错误
   - 参数类型断言需要安全处理
   - 返回有意义的错误信息

3. **参数验证**：
   - 必需参数使用 `mcp.Required()`
   - 提供详细的中文参数描述
   - 包含参数格式、限制等说明

4. **代码注释**：
   - 所有公开方法必须有注释
   - 注释使用中文
   - 说明方法功能和使用场景

### API文档：
```
{API_DOCUMENTATION}
```

### 生成要求：

请根据上述API文档生成以下文件的完整代码：

1. **常量定义**（添加到 `pkg/dingtalk/constant/api.go`）
2. **响应结构定义**（创建新文件或添加到现有文件）
3. **客户端方法实现**（添加到适当的 `pkg/dingtalk/` 文件）
4. **服务层实现**（创建新的service文件或添加到现有文件）
5. **main.go注册代码**（服务注册的代码片段）

### 额外要求：

- 分析API类型（查询/操作/管理），选择合适的HTTP方法
- 处理可选参数（使用指针类型或合理默认值）
- 如果API支持分页，实现分页逻辑
- 如果API有特殊的认证要求，在代码中体现
- 确保生成的工具名称在项目中是唯一的
- 提供清晰的工具描述，说明工具的用途和使用场景

请为每个生成的代码文件提供完整的代码，包括必要的import语句和包声明。