# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个钉钉 MCP（Message Connector Protocol）服务，基于 Go 1.24 开发。项目提供了与钉钉企业应用交互的 API 接口，支持员工信息查询和消息发送功能，通过 MCP 协议与 Claude Desktop 集成。

## 核心架构

### 目录结构
- `main.go` - 程序入口，配置 MCP 服务器和服务注册
- `internal/service/` - 业务服务层，包含员工和消息服务
- `pkg/dingtalk/` - 钉钉 SDK 封装，包含 API 客户端和数据模型
- `pkg/dingtalk/cache/` - 缓存系统，支持内存和文件缓存
- `pkg/dingtalk/models/` - 数据模型定义
- `pkg/dingtalk/constant/` - API 常量定义

### 核心组件
1. **DingTalk Client** (`pkg/dingtalk/dingtakl.go`): 钉钉 API 客户端，处理认证和 HTTP 请求
2. **Employee Service** (`internal/service/employee.go`): 员工管理服务，提供员工信息查询
3. **Message Service** (`internal/service/message.go`): 消息服务，支持文本和 Markdown 消息发送
4. **MCP 集成**: 使用 `github.com/mark3labs/mcp-go` 库实现 MCP 协议

## 开发命令

### 构建项目
```bash
go mod tidy
go build -o dingtalk-mcp .
```

### 运行项目
```bash
# 使用环境变量
export DINGTALK_AGENT_ID=your_agent_id
export DINGTALK_KEY=your_app_key
export DINGTALK_SECRET=your_app_secret
./dingtalk-mcp

# 或使用命令行参数
./dingtalk-mcp -id=your_agent_id -key=your_app_key -secret=your_app_secret
```

### 测试
项目目前包含基础的单元测试：
```bash
go test ./pkg/dingtalk/models/message/
```

## 可用的 MCP 工具

| 工具名称 | 功能描述 | 参数 |
|---------|----------|------|
| `get_employees_count` | 获取企业员工人数 | `only_active` (bool) - 是否只包含激活用户 |
| `get_simple_employees` | 获取企业员工基础信息 | 无参数 |
| `send_corp_conversation` | 发送文本消息 | `userIds` (string), `context` (string) |
| `send_markdown_corp_conversation` | 发送 Markdown 消息 | `userIds` (string), `title` (string), `content` (string) |
| `recall_corp_conversation` | 撤回消息 | `taskId` (string) |

## 配置要求

### 环境变量
- `DINGTALK_AGENT_ID`: 钉钉应用的 Agent ID
- `DINGTALK_KEY`: 钉钉应用的 App Key  
- `DINGTALK_SECRET`: 钉钉应用的 App Secret

### 钉钉应用权限
需要在钉钉开放平台配置以下权限：
- 通讯录只读权限
- 企业消息发送权限

## 缓存机制

项目实现了 Token 缓存机制，支持：
- 内存缓存 (`cache/memory/cache.go`)
- 文件缓存 (`cache/file/cache.go`) 
- 可扩展的缓存接口 (`cache/cache.go`)

Access Token 会自动缓存和刷新，避免频繁请求钉钉 API。

## API 版本支持

项目同时支持钉钉的新旧 API 版本：
- 新版 API: `/v1.0/` 和 `/v2.0/` 路径，使用 header 认证
- 旧版 API: 其他路径，使用 query 参数认证

## 开发文档

项目包含完整的标准化开发文档体系，位于 `docs/` 目录：

### 核心文档
- **[docs/COMPLETE_TOOL_GENERATOR.md](docs/COMPLETE_TOOL_GENERATOR.md)** - 完整的工具自动生成模板（推荐使用）
- **[docs/DEVELOPMENT_GUIDE.md](docs/DEVELOPMENT_GUIDE.md)** - 标准化开发流程指南
- **[docs/CODE_STANDARDS.md](docs/CODE_STANDARDS.md)** - 代码结构和命名规范
- **[docs/BEST_PRACTICES.md](docs/BEST_PRACTICES.md)** - 开发最佳实践指南

### 使用说明
- 新开发者：按顺序阅读所有文档以了解项目架构和规范
- 有经验的开发者：直接使用 `COMPLETE_TOOL_GENERATOR.md` 快速生成工具
- 项目维护者：使用文档进行代码审查和新成员培训

### 工具开发流程
1. 获取钉钉API文档
2. 使用 `docs/COMPLETE_TOOL_GENERATOR.md` 中的提示词模板
3. 将API文档替换模板中的占位符
4. 使用AI工具生成标准化代码
5. 按照生成的文件结构添加到项目中

## 代码生成规范

### 标准命名约定
- **常量**: `YourApiEndpointKey` (PascalCase + Key后缀)
- **结构体**: `YourService`, `YourApiResponse` (PascalCase)
- **方法**: `GetSomething`, `YourApiMethod` (PascalCase)
- **文件**: `your_service.go` (snake_case)
- **MCP工具**: `your_tool_name` (snake_case)

### 必需的代码结构
每个新工具都需要包含：
1. 常量定义（添加到 `pkg/dingtalk/constant/api.go`）
2. 响应结构定义（在 `pkg/dingtalk/response/` 中）
3. 客户端方法实现（在 `pkg/dingtalk/` 中）
4. 服务层实现（在 `internal/service/` 中）
5. 主程序注册（在 `main.go` 中）

### 质量要求
- 完整的中文注释和错误信息
- 全面的参数验证
- 统一的错误处理模式
- 符合项目的编码规范